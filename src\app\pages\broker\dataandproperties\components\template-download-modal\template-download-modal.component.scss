// Modern Template Download Modal Styles
:host {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1055;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Modal Backdrop
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  z-index: 1050;
}

// Modal Container
.modal {
  z-index: 1055;

  .modal-dialog {
    max-width: 500px;
    margin: 0 auto;

    .modal-content.modern-modal {
      border: none;
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      overflow: hidden;
      animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      // Modal Header
      .modal-header {
        background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
        padding: 2rem 2rem 1rem 2rem;
        position: relative;

        .modal-title-container {
          flex: 1;

          .modal-title {
            color: #1e293b;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;

            i {
              font-size: 1.3rem;
            }
          }

          .modal-subtitle {
            font-size: 0.95rem;
            color: #64748b;
            line-height: 1.5;
          }
        }

        .btn-close-custom {
          background: rgba(255, 255, 255, 0.9);
          border: none;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #64748b;
          transition: all 0.3s ease;
          position: absolute;
          top: 1rem;
          right: 1rem;

          &:hover {
            background: #ffffff;
            color: #dc3545;
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }

          i {
            font-size: 1rem;
          }
        }
      }

      // Modal Body
      .modal-body {
        padding: 1.5rem 2rem;

        .template-form {
          .form-group {
            .form-label {
              color: #374151;
              font-size: 1rem;
              display: flex;
              align-items: center;
              margin-bottom: 0.75rem;

              i {
                font-size: 1.1rem;
              }
            }

            .form-select {
              border-radius: 12px;
              border: 2px solid #e5e7eb;
              background-color: #ffffff;
              padding: 0.875rem 1rem;
              font-size: 0.95rem;
              color: #374151;
              transition: all 0.3s ease;

              &:focus {
                border-color: #10b981;
                box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.15);
                background-color: #ffffff;
              }

              &:hover {
                border-color: #d1d5db;
              }

              option {
                padding: 0.5rem;
                color: #374151;
              }
            }

            .form-select-solid {
              background-color: #f9fafb;
              border-color: #f3f4f6;

              &:focus {
                background-color: #ffffff;
                border-color: #10b981;
              }
            }
          }
        }
      }

      // Modal Footer
      .modal-footer {
        padding: 1rem 2rem 2rem 2rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);

        .btn {
          border-radius: 12px;
          font-weight: 600;
          font-size: 0.95rem;
          transition: all 0.3s ease;
          border: none;

          &.btn-light-secondary {
            background-color: #f1f5f9;
            color: #64748b;
            border: 2px solid #e2e8f0;

            &:hover:not(:disabled) {
              background-color: #e2e8f0;
              color: #475569;
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
          }

          &.btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;

            &:hover:not(:disabled) {
              background: linear-gradient(135deg, #059669 0%, #047857 100%);
              transform: translateY(-1px);
              box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
            }

            &:disabled {
              background: #d1d5db;
              color: #9ca3af;
              cursor: not-allowed;
            }
          }

          i {
            font-size: 0.9rem;
          }

          .spinner-border-sm {
            width: 1rem;
            height: 1rem;
          }
        }
      }
    }
  }
}

// Animation
@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// RTL Support
.rtl-layout {
  direction: rtl;
  text-align: right;

  .modal-content.modern-modal {
    .modal-header {
      .modal-title-container {
        .modal-title {
          flex-direction: row-reverse;

          i {
            margin-left: 0.75rem;
            margin-right: 0;
          }
        }
      }

      .btn-close-custom {
        left: 1rem;
        right: auto;
      }
    }

    .modal-body {
      .template-form {
        .form-group {
          .form-label {
            flex-direction: row-reverse;
            text-align: right;

            i {
              margin-left: 0;
              margin-right: 0.5rem;
            }
          }

          .form-select {
            text-align: right;
            direction: ltr;

            option {
              text-align: right;
              direction: rtl;
            }
          }
        }
      }
    }

    .modal-footer {
      .btn {
        i {
          margin-left: 0.5rem;
          margin-right: 0;
        }

        .spinner-border-sm {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }
  }
}

// Arabic Font Support
.rtl-layout {
  .modal-title,
  .modal-subtitle,
  .form-label,
  .btn {
    font-family: 'Noto Kufi Arabic', 'Tajawal', sans-serif;
  }

  .modal-title {
    font-weight: 700;
    font-size: 1.6rem;
  }

  .form-label {
    font-weight: 600;
    font-size: 1.05rem;
  }

  .btn {
    font-weight: 600;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .modal-dialog {
    max-width: 95%;
    margin: 1rem auto;
  }

  .modal-content.modern-modal {
    .modal-header {
      padding: 1.5rem 1.5rem 0.75rem 1.5rem;

      .modal-title-container {
        .modal-title {
          font-size: 1.3rem;
        }

        .modal-subtitle {
          font-size: 0.9rem;
        }
      }

      .btn-close-custom {
        width: 35px;
        height: 35px;
        top: 0.75rem;
        right: 0.75rem;
      }
    }

    .modal-body {
      padding: 1rem 1.5rem;
    }

    .modal-footer {
      padding: 0.75rem 1.5rem 1.5rem 1.5rem;

      .d-flex {
        flex-direction: column;
        gap: 0.75rem !important;
      }

      .btn {
        width: 100%;
      }
    }
  }

  // RTL Mobile adjustments
  .rtl-layout {
    .modal-content.modern-modal {
      .modal-header {
        .btn-close-custom {
          left: 0.75rem;
          right: auto;
        }
      }
    }
  }
}

// Enhanced hover effects
.modal-content.modern-modal {
  .modal-body {
    .form-group {
      .form-select {
        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
      }
    }
  }
}

// Loading state
.btn:disabled {
  .spinner-border {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
