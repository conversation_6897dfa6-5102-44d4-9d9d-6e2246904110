<!-- <PERSON><PERSON> Backdrop -->
<div class="modal-backdrop fade show" (click)="close.emit()"></div>

<!-- Modal Container -->
<div class="modal fade show d-block" tabindex="-1" role="dialog"
  [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content modern-modal">

      <!-- Modal Header -->
      <div class="modal-header border-0 pb-0">
        <div class="modal-title-container">
          <h3 class="modal-title fw-bold text-dark mb-2">
            <i class="fas fa-download text-success" [class.me-3]="translationService.getCurrentLanguage() === 'en'"
              [class.ms-3]="translationService.getCurrentLanguage() === 'ar'"></i>
            {{ getTranslatedText('TITLE') }}
          </h3>
          <p class="modal-subtitle text-muted mb-0">
            {{ getTranslatedText('SUBTITLE') }}
          </p>
        </div>
        <button type="button" class="btn-close-custom" (click)="close.emit()" aria-label="Close">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="modal-body pt-4">
        <form class="template-form">

          <!-- Compound Type -->
          <div class="form-group mb-4">
            <label class="form-label fw-semibold mb-3">
              <i class="fas fa-building text-primary" [class.me-2]="translationService.getCurrentLanguage() === 'en'"
                [class.ms-2]="translationService.getCurrentLanguage() === 'ar'"></i>
              {{ getTranslatedText('COMPOUND_TYPE') }}
            </label>
            <select class="form-select form-select-solid" [(ngModel)]="compoundType" name="compoundType">
              <option value="" disabled>{{ getTranslatedText('SELECT_COMPOUND_TYPE') }}</option>
              <option *ngFor="let option of compoundTypeOptions" [value]="option.value">
                {{ getTranslatedText(option.key) }}
              </option>
            </select>
          </div>

          <!-- Operation Type -->
          <div class="form-group mb-4">
            <label class="form-label fw-semibold mb-3">
              <i class="fas fa-handshake text-warning" [class.me-2]="translationService.getCurrentLanguage() === 'en'"
                [class.ms-2]="translationService.getCurrentLanguage() === 'ar'"></i>
              {{ getTranslatedText('OPERATION_TYPE') }}
            </label>
            <select class="form-select form-select-solid" [(ngModel)]="operationType" name="operationType">
              <option value="" disabled>{{ getTranslatedText('SELECT_OPERATION_TYPE') }}</option>
              <option *ngFor="let option of operationTypeOptions" [value]="option.value">
                {{ getTranslatedText(option.key) }}
              </option>
            </select>
          </div>

          <!-- Unit Type -->
          <div class="form-group mb-4">
            <label class="form-label fw-semibold mb-3">
              <i class="fas fa-home text-info" [class.me-2]="translationService.getCurrentLanguage() === 'en'"
                [class.ms-2]="translationService.getCurrentLanguage() === 'ar'"></i>
              {{ getTranslatedText('UNIT_TYPE') }}
            </label>
            <select class="form-select form-select-solid" [(ngModel)]="unitType" name="unitType">
              <option value="" disabled>{{ getTranslatedText('SELECT_UNIT_TYPE') }}</option>
              <option *ngFor="let option of unitTypeOptions" [value]="option.value">
                {{ getTranslatedUnitType(option.key) }}
              </option>
            </select>
          </div>

        </form>
      </div>

      <!-- Modal Footer -->
      <div class="modal-footer border-0 pt-0">
        <div class="d-flex gap-3 w-100">
          <button type="button" class="btn btn-light-secondary flex-fill py-3" (click)="close.emit()"
            [disabled]="isDownloading">
            <i class="fas fa-times" [class.me-2]="translationService.getCurrentLanguage() === 'en'"
              [class.ms-2]="translationService.getCurrentLanguage() === 'ar'"></i>
            {{ getTranslatedText('CANCEL') }}
          </button>

          <button type="button" class="btn btn-success flex-fill py-3" (click)="submit()"
            [disabled]="!unitType || !compoundType || !operationType || isDownloading">
            <span *ngIf="!isDownloading">
              <i class="fas fa-download" [class.me-2]="translationService.getCurrentLanguage() === 'en'"
                [class.ms-2]="translationService.getCurrentLanguage() === 'ar'"></i>
              {{ getTranslatedText('DOWNLOAD') }}
            </span>
            <span *ngIf="isDownloading">
              <span class="spinner-border spinner-border-sm"
                [class.me-2]="translationService.getCurrentLanguage() === 'en'"
                [class.ms-2]="translationService.getCurrentLanguage() === 'ar'" role="status"></span>
              {{ getTranslatedText('DOWNLOADING') }}
            </span>
          </button>
        </div>
      </div>

    </div>
  </div>
</div>