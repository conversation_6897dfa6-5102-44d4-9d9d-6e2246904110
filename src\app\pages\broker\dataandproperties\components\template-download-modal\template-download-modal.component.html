<div class="modal fade show d-block" tabindex="-1" role="dialog" style="background-color: rgba(0,0,0,0.5);">
  <div class="modal-dialog" role="document">
    <div class="modal-content p-4">
      <h5 class="modal-title mb-3">Download Template</h5>

      <div class="form-group mb-2">
        <label>Compound Type</label>
        <select class="form-control" [(ngModel)]="compoundType">
          <option *ngFor="let option of compoundTypeOptions" [value]="option.value">{{ option.key }}</option>
        </select>
      </div>

      <div class="form-group mb-3">
        <label>Operation Type</label>
        <select class="form-control" [(ngModel)]="operationType">
          <option *ngFor="let option of operationTypeOptions" [value]="option.value">{{ option.key }}</option>
        </select>
      </div>

       <div class="form-group mb-2">
        <label>Unit Type</label>
        <select class="form-control" [(ngModel)]="unitType">
          <option *ngFor="let option of unitTypeOptions" [value]="option.value">{{ option.key }}</option>
        </select>
      </div>

      <div class="text-end">
        <button class="btn btn-secondary me-2" (click)="close.emit()">Cancel</button>
        <button class="btn btn-primary" (click)="submit()">Download</button>
      </div>
    </div>
  </div>
</div>
