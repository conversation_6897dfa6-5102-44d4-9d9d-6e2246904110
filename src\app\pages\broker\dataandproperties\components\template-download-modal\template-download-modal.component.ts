import { Component, EventEmitter, Output } from '@angular/core';

@Component({
  selector: 'app-template-download-modal',
  templateUrl: './template-download-modal.component.html',
  styleUrl: './template-download-modal.component.scss'
})
export class TemplateDownloadModalComponent {
  unitType: string = '';
  compoundType: string = '';
  operationType: string = '';

  unitTypeOptions = [
    { key: 'APARTMENTS', value: 'apartments' },
    { key: 'DUPLEXES', value: 'duplexes' },
    { key: 'STUDIOS', value: 'studios' },
    { key: 'PENTHOUSES', value: 'penthouses' },
    { key: 'ROOFS', value: 'roofs' },
    { key: 'VILLAS', value: 'villas' },
    { key: 'I_VILLA', value: 'i_villa' },
    { key: 'TWIN_HOUSES', value: 'twin_houses' },
    { key: 'TOWN_HOUSES', value: 'town_houses' },
    { key: 'ADMINISTRATIVE_UNITS', value: 'administrative_units' },
    { key: 'MEDICAL_CLINICS', value: 'medical_clinics' },
    { key: 'PHARMACIES', value: 'pharmacies' },
    { key: 'COMMERCIAL_STORES', value: 'commercial_stores' },
    { key: 'WAREHOUSES', value: 'warehouses' },
    { key: 'FACTORY_LANDS', value: 'factory_lands' },
    { key: 'WAREHOUSES_LAND', value: 'warehouses_land' },
    { key: 'STANDALONE_VILLAS', value: 'standalone_villas' },
    { key: 'COMMERCIAL_ADMINISTRATIVE_BUILDINGS', value: 'commercial_administrative_buildings' },
    { key: 'COMMERCIAL_ADMINISTRATIVE_LANDS', value: 'commercial_administrative_lands' },
    { key: 'RESIDENTIAL_BUILDINGS', value: 'residential_buildings' },
    { key: 'RESIDENTIAL_LANDS', value: 'residential_lands' },
    { key: 'CHALETS', value: 'chalets' },
    { key: 'HOTELS', value: 'hotels' },
    { key: 'FACTORIES', value: 'factories' },
    { key: 'BASEMENTS', value: 'basements' },
    { key: 'FULL_BUILDINGS', value: 'full_buildings' },
    { key: 'COMMERCIAL_UNITS', value: 'commercial_units' },
    { key: 'SHOPS', value: 'shops' },
    { key: 'MIXED_HOUSINGS', value: 'mixed_housings' },
    { key: 'COOPERATIVES', value: 'cooperatives' },
    { key: 'YOUTH_UNITS', value: 'youth_units' },
    { key: 'GANAT_MISR', value: 'ganat_misr' },
    { key: 'DAR_MISR', value: 'dar_misr' },
    { key: 'SAKAN_MISR', value: 'sakan_misr' },
    { key: 'INDUSTRIAL_LANDS', value: 'industrial_lands' },
    { key: 'CABIN', value: 'cabin' },
    { key: 'VACATION_VILLA', value: 'vacation_villa' },
    { key: 'RESIDENTIAL_VILLA_LANDS', value: 'residential_villa_lands' },
    { key: 'RESIDENTIAL_BUILDINGS_LANDS', value: 'residential_buildings_lands' },
    { key: 'ADMINSTRATIVE_LANDS', value: 'adminstrative_lands' },
    { key: 'COMMERCIAL_LANDS', value: 'commercial_lands' },
    { key: 'MEDICAL_LANDS', value: 'medical_lands' },
    { key: 'MIXED_LANDS', value: 'mixed_lands' },
  ];

  compoundTypeOptions: { key: string; value: string }[] = [
    { key: 'Outside Compound', value: 'outside_compound' },
    { key: 'Inside Compound', value: 'inside_compound' },
  ];

  operationTypeOptions: { key: string; value: string }[] = [
    { key: 'Sell', value: 'sell' },
    { key: 'Rent', value: 'rent_out' },
  ];

  @Output() download = new EventEmitter<{
    unitType: string;
    compoundType: string;
    operationType: string;
  }>();

  @Output() close = new EventEmitter<void>();

  submit() {
    this.download.emit({
      unitType: this.unitType,
      compoundType: this.compoundType,
      operationType: this.operationType,
    });
  }
}

